// API Response Types
export interface UserResponse {
  id: string
  name: string
  login: string
  email: string
  role: string
  contactNo: string
  notification: boolean
  createdAt: string
  updatedAt: string
}

export interface Customer {
  id: string
  parentId?: string
  name: string
  contactPerson: string
  contactPerson2?: string
  contactNo: string
  contactNo2?: string
  email: string
  email2?: string
  address1: string
  address2?: string
  postcode: string
  city: string
  state: string
  country: string
  createBy: string
  isDelete: boolean
  officeNo?: string
  officeNo2?: string
  fax?: string
  officeExtNo?: string
  officeExtNo2?: string
  fullName?: string
  createdAt: string
  updatedAt: string
}

// API Request Types
export interface CreateCustomerRequest {
  parentId?: string
  name: string
  contactPerson: string
  contactPerson2?: string
  contactNo: string
  contactNo2?: string
  email: string
  email2?: string
  address1: string
  address2?: string
  postcode: string
  city: string
  state: string
  country: string
  officeNo?: string
  officeNo2?: string
  fax?: string
  officeExtNo?: string
  officeExtNo2?: string
  fullName?: string
}

export interface UpdateCustomerRequest extends Partial<CreateCustomerRequest> {}

// Pagination information
export interface PaginationInfo {
  page: number
  limit: number
  total: number
  pages: number
}

// Inner API response structure (what the server actually returns)
export interface InnerApiResponse<T> {
  success: boolean
  data: T | null
  pagination?: PaginationInfo
  timestamp: string
}

// Outer API response wrapper (what we get from the HTTP request)
export interface ApiResponse<T> {
  success: boolean
  data?: InnerApiResponse<T>
  message?: string
  error?: string
}

export interface Equipment {
  id: string
  vendorId: string
  category0: string
  category1: string
  category2: string
  orderId: string
  name: string
  sn?: string
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
  createBy: string
  isDelete: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateEquipmentRequest {
  vendorId: string
  category0: string
  category1: string
  category2: string
  orderId: string
  name: string
  sn?: string
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
}

export interface UpdateEquipmentRequest extends Partial<CreateEquipmentRequest> {}

export interface Event {
  id: string
  title: string
  description: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  equipmentId?: string
  customerId?: string
  createdAt: Date
  updatedAt: Date
}

export interface Ticket {
  id: string
  ticketno: string
  title: string
  description?: string
  level?: string
  customerPackage?: string
  department?: string
  assignee?: string[]
  attachments?: string[]
  createBy: string
  customer: string
  closingdate?: string
  statustxt?: string
  createdAt: string
  updatedAt: string
}

export interface CreateTicketRequest {
  ticketno: string
  title: string
  description?: string
  level?: string
  customerPackage?: string
  department?: string
  assignee?: string[]
  attachments?: string[]
  customer: string
  statustxt?: string
}

export interface UpdateTicketRequest extends Partial<CreateTicketRequest> {}

export interface CalendarItem {
  id: string
  title: string
  date: Date
  type: 'event' | 'equipment-expiry' | 'reminder'
  color: 'blue' | 'red' | 'green'
  relatedId?: string
}

// Customer Package Types
export interface CustomerPackage {
  id: string
  customerId: string
  packageId: string
  circuitId?: string
  wanIp?: string
  lanIp?: string
  servicesNo?: string
  loginId?: string
  password?: string
  accountNo?: string
  orderNo?: string
  speed?: string
  provider?: string
  brandModem?: string
  modemLogin?: string
  remark?: string
  createBy: string
  isDelete: boolean
  companyRegNo?: string
  companyName?: string
  serviceTag?: string
  orderStageId?: string
  activationDate?: string
  contractEndDate?: string
  status: number
  terminateAlertFrom?: string
  terminateAlertTo?: string
  isNotify: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateCustomerPackageRequest {
  customerId: string
  packageId: string
  circuitId?: string
  wanIp?: string
  lanIp?: string
  servicesNo?: string
  loginId?: string
  password?: string
  accountNo?: string
  orderNo?: string
  speed?: string
  provider?: string
  brandModem?: string
  modemLogin?: string
  remark?: string
  companyRegNo?: string
  companyName?: string
  serviceTag?: string
  orderStageId?: string
  activationDate?: string
  contractEndDate?: string
  status: number
  terminateAlertFrom?: string
  terminateAlertTo?: string
  isNotify?: boolean
}

export interface UpdateCustomerPackageRequest extends Partial<CreateCustomerPackageRequest> {}

// Customer Document Types
export interface CustomerDocument {
  id: string
  customerId: string
  fileId: string
  docType: number
  name: string
  remark?: string
  status: number
  createBy: string
  isDelete: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateCustomerDocumentRequest {
  customerId: string
  fileId: string
  docType: number
  name: string
  remark?: string
  status: number
}

export interface UpdateCustomerDocumentRequest extends Partial<CreateCustomerDocumentRequest> {}

// Customer Services Types
export interface CustomerServices {
  id: string
  ticketNo?: string
  customerId: string
  status: number
  reportBy?: string
  reportContact?: string
  outageCategory?: string
  circuitId?: string
  telcoReportNo?: string
  telcoPerson?: string
  telcoContact?: string
  createBy: string
  isDelete: boolean
  packageId?: string
  createdAt: string
  updatedAt: string
}

export interface CreateCustomerServicesRequest {
  ticketNo?: string
  customerId: string
  status: number
  reportBy?: string
  reportContact?: string
  outageCategory?: string
  circuitId?: string
  telcoReportNo?: string
  telcoPerson?: string
  telcoContact?: string
  packageId?: string
}

export interface UpdateCustomerServicesRequest extends Partial<CreateCustomerServicesRequest> {}

// Equipment Inventory Types
export interface EquipmentInventory {
  id: string
  equipmentId: string
  customerId: string
  orderId?: string
  sn?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
  createBy: string
  isDelete: boolean
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  orderStageId?: string
  orderStage2EquipmentId?: string
  maintenanceVendorId?: string
  maintenanceExpired?: string
  createdAt: string
  updatedAt: string
}

export interface CreateEquipmentInventoryRequest {
  equipmentId: string
  customerId: string
  orderId?: string
  sn?: string
  status: number
  remark?: string
  stockinDate?: string
  stockinBy?: string
  stockoutDate?: string
  stockoutBy?: string
  warrantyPeriod?: number
  warrantyStartDate?: string
  warrantyExpDate?: string
  orderStageId?: string
  orderStage2EquipmentId?: string
  maintenanceVendorId?: string
  maintenanceExpired?: string
}

export interface UpdateEquipmentInventoryRequest extends Partial<CreateEquipmentInventoryRequest> {}

// Equipment Category Types
export interface EquipmentCategory {
  id: string
  parentId?: string
  name: string
  categoryType: number
  isDelete: boolean
  createdAt: string
  updatedAt: string
}

export interface CreateEquipmentCategoryRequest {
  parentId?: string
  name: string
  categoryType: number
}

export interface UpdateEquipmentCategoryRequest extends Partial<CreateEquipmentCategoryRequest> {}
