<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-lg p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-6">
          <div class="flex items-center space-x-3">
            <div class="h-12 w-12 rounded-full bg-blue-500 flex items-center justify-center">
              <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-900">{{ ticket.title }}</h3>
              <p class="text-sm text-gray-500">Ticket Details</p>
            </div>
          </div>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div class="space-y-6">
          <!-- Status and Priority -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Status & Priority</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="flex items-center justify-between">
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">Status</label>
                  <span
                    class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                    :class="{
                      'bg-yellow-100 text-yellow-800': ticket.status === 'open',
                      'bg-blue-100 text-blue-800': ticket.status === 'in-progress',
                      'bg-green-100 text-green-800': ticket.status === 'resolved',
                      'bg-gray-100 text-gray-800': ticket.status === 'closed'
                    }"
                  >
                    {{ ticket.status.replace('-', ' ') }}
                  </span>
                </div>
                <div>
                  <label class="block text-xs font-medium text-gray-500 mb-1">Priority</label>
                  <span
                    class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                    :class="{
                      'bg-red-100 text-red-800': ticket.priority === 'urgent',
                      'bg-orange-100 text-orange-800': ticket.priority === 'high',
                      'bg-yellow-100 text-yellow-800': ticket.priority === 'medium',
                      'bg-green-100 text-green-800': ticket.priority === 'low'
                    }"
                  >
                    {{ ticket.priority }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- Description -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Description</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <p class="text-sm text-gray-900 whitespace-pre-wrap">{{ ticket.description }}</p>
            </div>
          </div>

          <!-- Customer Information -->
          <div v-if="customerInfo">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Customer Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="flex items-center space-x-3">
                <div class="h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center">
                  <span class="text-xs font-medium text-white">
                    {{ customerInfo.name.charAt(0).toUpperCase() }}
                  </span>
                </div>
                <div>
                  <p class="text-sm font-medium text-gray-900">{{ customerInfo.name }}</p>
                  <p class="text-xs text-gray-500">{{ customerInfo.company || 'No Company' }}</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Equipment Information -->
          <div v-if="equipmentInfo">
            <h4 class="text-sm font-medium text-gray-900 mb-3">Equipment Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div>
                <p class="text-sm font-medium text-gray-900">{{ equipmentInfo.name }}</p>
                <p class="text-xs text-gray-500">{{ equipmentInfo.model }} • {{ equipmentInfo.serialNumber }}</p>
              </div>
            </div>
          </div>

          <!-- Assignment Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Assignment Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Created By:</span>
                <span class="text-gray-900">{{ ticket.createdBy }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Assigned To:</span>
                <span class="text-gray-900">{{ ticket.assignedTo || 'Unassigned' }}</span>
              </div>
            </div>
          </div>

          <!-- Date Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Date Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg space-y-2">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Created:</span>
                <span class="text-gray-900">{{ formatDate(ticket.createdAt) }}</span>
              </div>
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Last Updated:</span>
                <span class="text-gray-900">{{ formatDate(ticket.updatedAt) }}</span>
              </div>
              <div v-if="ticket.resolvedAt" class="flex justify-between text-sm">
                <span class="text-gray-500">Resolved:</span>
                <span class="text-gray-900">{{ formatDate(ticket.resolvedAt) }}</span>
              </div>
            </div>
          </div>

          <!-- System Information -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">System Information</h4>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="flex justify-between text-sm">
                <span class="text-gray-500">Ticket ID:</span>
                <span class="text-gray-900 font-mono">{{ ticket.id }}</span>
              </div>
            </div>
          </div>
        </div>

        <div class="flex justify-end space-x-3 pt-6">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
          >
            Close
          </button>
          <button
            @click="$emit('edit', ticket)"
            class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
          >
            Edit Ticket
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
// import { useCustomersStore } from '@/stores/customers'
// import { useEquipmentStore } from '@/stores/equipment'
import type { Ticket } from '@/types'

const props = defineProps<{
  ticket: Ticket
}>()

const emit = defineEmits<{
  close: []
  edit: [ticket: Ticket]
}>()

// const customersStore = useCustomersStore()
// const equipmentStore = useEquipmentStore()

const customerInfo = computed(() => {
  if (props.ticket.customerId) {
    return customersStore.getCustomerById(props.ticket.customerId)
  }
  return null
})

const equipmentInfo = computed(() => {
  if (props.ticket.equipmentId) {
    return equipmentStore.getEquipmentById(props.ticket.equipmentId)
  }
  return null
})

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}
</script>
