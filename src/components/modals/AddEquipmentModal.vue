<template>
  <div class="fixed inset-0 z-50 overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-md p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900">Add New Equipment</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Equipment Name *</label>
            <input
              id="name"
              v-model="form.name"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter equipment name"
            />
          </div>

          <div>
            <label for="model" class="block text-sm font-medium text-gray-700 mb-1">Model *</label>
            <input
              id="model"
              v-model="form.model"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter model number"
            />
          </div>

          <div>
            <label for="serialNumber" class="block text-sm font-medium text-gray-700 mb-1">Serial Number *</label>
            <input
              id="serialNumber"
              v-model="form.serialNumber"
              type="text"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter serial number"
            />
          </div>

          <div>
            <label for="customerId" class="block text-sm font-medium text-gray-700 mb-1">Customer *</label>
            <select
              id="customerId"
              v-model="form.customerId"
              required
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="">Select a customer</option>
              <option
                v-for="customer in customersStore.customers"
                :key="customer.id"
                :value="customer.id"
              >
                {{ customer.name }} - {{ customer.company || 'No Company' }}
              </option>
            </select>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label for="purchaseDate" class="block text-sm font-medium text-gray-700 mb-1">Purchase Date *</label>
              <input
                id="purchaseDate"
                v-model="form.purchaseDate"
                type="date"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label for="expiryDate" class="block text-sm font-medium text-gray-700 mb-1">Expiry Date *</label>
              <input
                id="expiryDate"
                v-model="form.expiryDate"
                type="date"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label for="status" class="block text-sm font-medium text-gray-700 mb-1">Status</label>
            <select
              id="status"
              v-model="form.status"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="maintenance">Maintenance</option>
            </select>
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700 mb-1">Description</label>
            <textarea
              id="description"
              v-model="form.description"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter equipment description"
            ></textarea>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              Add Equipment
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
// // import { useEquipmentStore } from '@/stores/equipment'
// // import { useCustomersStore } from '@/stores/customers'
import type { Equipment } from '@/types'

const emit = defineEmits<{
  close: []
}>()

// // const equipmentStore = useEquipmentStore()
// // const customersStore = useCustomersStore()
const customers = ref<any[]>([])

const form = ref({
  name: '',
  model: '',
  serialNumber: '',
  customerId: '',
  purchaseDate: new Date().toISOString().split('T')[0],
  expiryDate: '',
  status: 'active' as Equipment['status'],
  description: ''
})

const handleSubmit = () => {
  const equipmentData = {
    name: form.value.name,
    model: form.value.model,
    serialNumber: form.value.serialNumber,
    customerId: form.value.customerId,
    purchaseDate: new Date(form.value.purchaseDate),
    expiryDate: new Date(form.value.expiryDate),
    status: form.value.status,
    description: form.value.description || undefined
  }

  equipmentStore.addEquipment(equipmentData)
  emit('close')
}

onMounted(() => {
  // Set default expiry date to 3 years from purchase date
  const threeYearsFromNow = new Date()
  threeYearsFromNow.setFullYear(threeYearsFromNow.getFullYear() + 3)
  form.value.expiryDate = threeYearsFromNow.toISOString().split('T')[0]
  
  // Initialize customers data if needed
  customersStore.initializeSampleData()
})
</script>
