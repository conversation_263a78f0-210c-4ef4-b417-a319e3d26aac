<template>
  <div class="fixed inset-0 z-[90] overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0">
      <!-- Background overlay -->
      <div class="fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75" @click="$emit('close')"></div>

      <!-- Modal -->
      <div class="inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg max-h-[90vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">Add New Customer</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Basic Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-1">Company Name *</label>
                <input
                  id="name"
                  v-model="form.name"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter company name"
                />
              </div>
              <div>
                <label for="fullName" class="block text-sm font-medium text-gray-700 mb-1">Full Name</label>
                <input
                  id="fullName"
                  v-model="form.fullName"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter full name"
                />
              </div>
            </div>
          </div>

          <!-- Contact Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Contact Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="contactPerson" class="block text-sm font-medium text-gray-700 mb-1">Contact Person *</label>
                <input
                  id="contactPerson"
                  v-model="form.contactPerson"
                  type="text"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter contact person name"
                />
              </div>
              <div>
                <label for="contactPerson2" class="block text-sm font-medium text-gray-700 mb-1">Contact Person 2</label>
                <input
                  id="contactPerson2"
                  v-model="form.contactPerson2"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter second contact person name"
                />
              </div>
              <div>
                <label for="contactNo" class="block text-sm font-medium text-gray-700 mb-1">Contact Number *</label>
                <input
                  id="contactNo"
                  v-model="form.contactNo"
                  type="tel"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter contact number"
                />
              </div>
              <div>
                <label for="contactNo2" class="block text-sm font-medium text-gray-700 mb-1">Contact Number 2</label>
                <input
                  id="contactNo2"
                  v-model="form.contactNo2"
                  type="tel"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter second contact number"
                />
              </div>
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700 mb-1">Email</label>
                <input
                  id="email"
                  v-model="form.email"
                  type="email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter email address"
                />
              </div>
              <div>
                <label for="email2" class="block text-sm font-medium text-gray-700 mb-1">Email 2</label>
                <input
                  id="email2"
                  v-model="form.email2"
                  type="email"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter second email address"
                />
              </div>
            </div>
          </div>

          <!-- Office Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Office Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="officeNo" class="block text-sm font-medium text-gray-700 mb-1">Office Number</label>
                <input
                  id="officeNo"
                  v-model="form.officeNo"
                  type="tel"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter office number"
                />
              </div>
              <div>
                <label for="officeNo2" class="block text-sm font-medium text-gray-700 mb-1">Office Number 2</label>
                <input
                  id="officeNo2"
                  v-model="form.officeNo2"
                  type="tel"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter second office number"
                />
              </div>
              <div>
                <label for="officeExtNo" class="block text-sm font-medium text-gray-700 mb-1">Office Extension</label>
                <input
                  id="officeExtNo"
                  v-model="form.officeExtNo"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter office extension"
                />
              </div>
              <div>
                <label for="officeExtNo2" class="block text-sm font-medium text-gray-700 mb-1">Office Extension 2</label>
                <input
                  id="officeExtNo2"
                  v-model="form.officeExtNo2"
                  type="text"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter second office extension"
                />
              </div>
              <div>
                <label for="fax" class="block text-sm font-medium text-gray-700 mb-1">Fax</label>
                <input
                  id="fax"
                  v-model="form.fax"
                  type="tel"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter fax number"
                />
              </div>
            </div>
          </div>

          <!-- Address Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Address Information</h4>
            <div class="space-y-4">
              <div>
                <label for="address1" class="block text-sm font-medium text-gray-700 mb-1">Address 1 *</label>
                <textarea
                  id="address1"
                  v-model="form.address1"
                  rows="2"
                  required
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter primary address"
                ></textarea>
              </div>
              <div>
                <label for="address2" class="block text-sm font-medium text-gray-700 mb-1">Address 2</label>
                <textarea
                  id="address2"
                  v-model="form.address2"
                  rows="2"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Enter secondary address"
                ></textarea>
              </div>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label for="city" class="block text-sm font-medium text-gray-700 mb-1">City *</label>
                  <input
                    id="city"
                    v-model="form.city"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter city"
                  />
                </div>
                <div>
                  <label for="postcode" class="block text-sm font-medium text-gray-700 mb-1">Postcode *</label>
                  <input
                    id="postcode"
                    v-model="form.postcode"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter postcode"
                  />
                </div>
                <div>
                  <label for="state" class="block text-sm font-medium text-gray-700 mb-1">State *</label>
                  <input
                    id="state"
                    v-model="form.state"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter state"
                  />
                </div>
                <div>
                  <label for="country" class="block text-sm font-medium text-gray-700 mb-1">Country *</label>
                  <input
                    id="country"
                    v-model="form.country"
                    type="text"
                    required
                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Enter country"
                  />
                </div>
              </div>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-6">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 rounded-md hover:bg-blue-700 transition-colors"
            >
              Add Customer
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { CustomerService } from '@/services/api'

const emit = defineEmits<{
  close: []
}>()

const form = ref({
  name: '',
  contactPerson: '',
  contactPerson2: '',
  contactNo: '',
  contactNo2: '',
  email: '',
  email2: '',
  address1: '',
  address2: '',
  postcode: '',
  city: '',
  state: '',
  country: '',
  officeNo: '',
  officeNo2: '',
  fax: '',
  officeExtNo: '',
  officeExtNo2: '',
  fullName: ''
})

const handleSubmit = async () => {
  try {
    const customerData = {
      name: form.value.name,
      contactPerson: form.value.contactPerson,
      contactPerson2: form.value.contactPerson2,
      contactNo: form.value.contactNo,
      contactNo2: form.value.contactNo2,
      email: form.value.email,
      email2: form.value.email2,
      address1: form.value.address1,
      address2: form.value.address2,
      postcode: form.value.postcode,
      city: form.value.city,
      state: form.value.state,
      country: form.value.country,
      officeNo: form.value.officeNo,
      officeNo2: form.value.officeNo2,
      fax: form.value.fax,
      officeExtNo: form.value.officeExtNo,
      officeExtNo2: form.value.officeExtNo2,
      fullName: form.value.fullName
    }

    const response = await CustomerService.createCustomer(customerData)
    if (response.success && response.data && response.data.success) {
      emit('close')
    } else {
      alert('Failed to create customer: ' + (response.error || 'Unknown error'))
    }
  } catch (error) {
    alert('Failed to create customer: ' + (error instanceof Error ? error.message : 'Unknown error'))
  }
}
</script>
