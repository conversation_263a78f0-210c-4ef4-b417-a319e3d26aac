<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">Add Customer Package</h3>
          <button
            @click="$emit('close')"
            class="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Form -->
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Customer -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Customer *</label>
              <select
                v-model="form.customerId"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select Customer</option>
                <option v-for="customer in customers" :key="customer.id" :value="customer.id">
                  {{ customer.name }}
                </option>
              </select>
            </div>

            <!-- Package ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Package ID *</label>
              <input
                v-model="form.packageId"
                type="text"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter package ID"
              />
            </div>

            <!-- Circuit ID -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Circuit ID</label>
              <input
                v-model="form.circuitId"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter circuit ID"
              />
            </div>

            <!-- Services No -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Services No</label>
              <input
                v-model="form.servicesNo"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter services number"
              />
            </div>

            <!-- WAN IP -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">WAN IP</label>
              <input
                v-model="form.wanIp"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter WAN IP"
              />
            </div>

            <!-- LAN IP -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">LAN IP</label>
              <input
                v-model="form.lanIp"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter LAN IP"
              />
            </div>

            <!-- Speed -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Speed</label>
              <input
                v-model="form.speed"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter speed"
              />
            </div>

            <!-- Provider -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Provider</label>
              <input
                v-model="form.provider"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter provider"
              />
            </div>

            <!-- Company Name -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
              <input
                v-model="form.companyName"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter company name"
              />
            </div>

            <!-- Company Reg No -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Company Reg No</label>
              <input
                v-model="form.companyRegNo"
                type="text"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter company registration number"
              />
            </div>

            <!-- Status -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">Status *</label>
              <select
                v-model="form.status"
                required
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option :value="0">Inactive</option>
                <option :value="1">Active</option>
                <option :value="2">Suspended</option>
              </select>
            </div>

            <!-- Is Notify -->
            <div class="flex items-center">
              <input
                v-model="form.isNotify"
                type="checkbox"
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label class="ml-2 block text-sm text-gray-900">Enable Notifications</label>
            </div>
          </div>

          <!-- Remark -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">Remark</label>
            <textarea
              v-model="form.remark"
              rows="3"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="Enter remarks"
            ></textarea>
          </div>

          <!-- Error Message -->
          <div v-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error</h3>
                <div class="mt-2 text-sm text-red-700">{{ error }}</div>
              </div>
            </div>
          </div>

          <!-- Actions -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <button
              type="button"
              @click="$emit('close')"
              class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
            >
              {{ loading ? 'Creating...' : 'Create Package' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CustomerPackageService, CustomerService } from '@/services/api'
import type { CreateCustomerPackageRequest, Customer } from '@/types'

// Emits
const emit = defineEmits<{
  close: []
  success: []
}>()

// Reactive data
const customers = ref<Customer[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const form = ref<CreateCustomerPackageRequest>({
  customerId: '',
  packageId: '',
  circuitId: '',
  wanIp: '',
  lanIp: '',
  servicesNo: '',
  speed: '',
  provider: '',
  companyName: '',
  companyRegNo: '',
  status: 1,
  isNotify: false,
  remark: ''
})

// Methods
const handleSubmit = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await CustomerPackageService.createCustomerPackage(form.value)
    if (response.success) {
      emit('success')
      emit('close')
    } else {
      error.value = response.error || 'Failed to create package'
    }
  } catch (err) {
    error.value = 'Failed to create package'
    console.error('Create package error:', err)
  } finally {
    loading.value = false
  }
}

const loadCustomers = async () => {
  try {
    const response = await CustomerService.getCustomers()
    if (response.success && response.data?.success && response.data.data) {
      customers.value = response.data.data
    }
  } catch (err) {
    console.error('Load customers error:', err)
  }
}

onMounted(() => {
  loadCustomers()
})
</script>
