<template>
  <div class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
      <div class="mt-3">
        <!-- Header -->
        <div class="flex items-center justify-between mb-6">
          <h3 class="text-lg font-medium text-gray-900">Customer Package Details</h3>
          <div class="flex items-center space-x-2">
            <button
              @click="$emit('edit', package)"
              class="text-indigo-600 hover:text-indigo-900 transition-colors"
              title="Edit Package"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button
              @click="$emit('close')"
              class="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <!-- Package Details -->
        <div class="space-y-6">
          <!-- Basic Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Basic Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Customer</label>
                <p class="mt-1 text-sm text-gray-900">{{ getCustomerName(package.customerId) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Package ID</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.packageId }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Circuit ID</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.circuitId || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Services No</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.servicesNo || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Status</label>
                <span :class="getStatusClass(package.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getStatusText(package.status) }}
                </span>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Notifications</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.isNotify ? 'Enabled' : 'Disabled' }}</p>
              </div>
            </div>
          </div>

          <!-- Network Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Network Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">WAN IP</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.wanIp || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">LAN IP</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.lanIp || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Speed</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.speed || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Provider</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.provider || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Account Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Account Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Login ID</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.loginId || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Password</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.password ? '••••••••' : 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Account No</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.accountNo || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Order No</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.orderNo || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Company Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Company Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Company Name</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.companyName || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Company Reg No</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.companyRegNo || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Service Tag</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.serviceTag || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Order Stage ID</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.orderStageId || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Equipment Information -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Equipment Information</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Brand Modem</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.brandModem || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Modem Login</label>
                <p class="mt-1 text-sm text-gray-900">{{ package.modemLogin || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Dates -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Important Dates</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Activation Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(package.activationDate) || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Contract End Date</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(package.contractEndDate) || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Terminate Alert From</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(package.terminateAlertFrom) || 'N/A' }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Terminate Alert To</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(package.terminateAlertTo) || 'N/A' }}</p>
              </div>
            </div>
          </div>

          <!-- Remark -->
          <div v-if="package.remark" class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-2">Remark</h4>
            <p class="text-sm text-gray-900">{{ package.remark }}</p>
          </div>

          <!-- Metadata -->
          <div class="bg-gray-50 p-4 rounded-lg">
            <h4 class="text-md font-medium text-gray-900 mb-4">Metadata</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-500">Created At</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(package.createdAt) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-500">Updated At</label>
                <p class="mt-1 text-sm text-gray-900">{{ formatDate(package.updatedAt) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="flex justify-end space-x-3 pt-6 border-t mt-6">
          <button
            @click="$emit('close')"
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
          <button
            @click="$emit('edit', package)"
            class="px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
          >
            Edit Package
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { CustomerService } from '@/services/api'
import type { CustomerPackage, Customer } from '@/types'

// Props
const props = defineProps<{
  package: CustomerPackage
}>()

// Emits
const emit = defineEmits<{
  close: []
  edit: [package: CustomerPackage]
}>()

// Reactive data
const customers = ref<Customer[]>([])

// Helper functions
const getCustomerName = (customerId: string): string => {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.name || 'Unknown Customer'
}

const getStatusText = (status: number): string => {
  switch (status) {
    case 0: return 'Inactive'
    case 1: return 'Active'
    case 2: return 'Suspended'
    default: return 'Unknown'
  }
}

const getStatusClass = (status: number): string => {
  switch (status) {
    case 0: return 'bg-gray-100 text-gray-800'
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString?: string): string => {
  if (!dateString) return ''
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}

const loadCustomers = async () => {
  try {
    const response = await CustomerService.getCustomers()
    if (response.success && response.data?.success && response.data.data) {
      customers.value = response.data.data
    }
  } catch (err) {
    console.error('Load customers error:', err)
  }
}

onMounted(() => {
  loadCustomers()
})
</script>
