<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Tickets</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Create Ticket
      </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-5">
      <div class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="p-2 md:p-3 bg-yellow-100 rounded-lg">
            <svg class="w-6 h-6 md:w-7 md:h-7 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4 md:ml-5">
            <p class="text-sm md:text-base font-medium text-gray-600">Open</p>
            <p class="text-lg font-semibold text-gray-900">{{ ticketStats.open }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">In Progress</p>
            <p class="text-lg font-semibold text-gray-900">{{ ticketStats.inProgress }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Resolved</p>
            <p class="text-lg font-semibold text-gray-900">{{ ticketStats.resolved }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 rounded-lg">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Urgent</p>
            <p class="text-lg font-semibold text-gray-900">{{ urgentTickets.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search tickets..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <select
          v-model="statusFilter"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Status</option>
          <option value="open">Open</option>
          <option value="in-progress">In Progress</option>
          <option value="resolved">Resolved</option>
          <option value="closed">Closed</option>
        </select>
        <select
          v-model="levelFilter"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Levels</option>
          <option value="urgent">Urgent</option>
          <option value="high">High</option>
          <option value="medium">Medium</option>
          <option value="low">Low</option>
        </select>
        <select
          v-model="departmentFilter"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Departments</option>
          <option value="technical">Technical</option>
          <option value="support">Support</option>
          <option value="sales">Sales</option>
          <option value="billing">Billing</option>
        </select>
        <button
          @click="clearFilters"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Clear
        </button>
      </div>
    </div>

    <!-- Tickets Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Tickets ({{ filteredTickets.length }})
        </h3>
      </div>

      <div v-if="filteredTickets.length === 0" class="p-6 text-center text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 5v2m0 4v2m0 4v2M5 5a2 2 0 00-2 2v3a2 2 0 110 4v3a2 2 0 002 2h14a2 2 0 002-2v-3a2 2 0 110-4V7a2 2 0 00-2-2H5z" />
        </svg>
        <p class="mt-2">No tickets found</p>
        <p class="text-sm text-gray-400">Get started by creating your first ticket</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ticket
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Level
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Department
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="ticket in filteredTickets"
              :key="ticket.id"
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ ticket.ticketno }}</div>
                  <div class="text-sm font-medium text-gray-900">{{ ticket.title }}</div>
                  <div class="text-sm text-gray-500 truncate max-w-xs">{{ ticket.description || 'No description' }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ getCustomerName(ticket.customer) }}</div>
                <div v-if="ticket.customerPackage" class="text-sm text-gray-500">{{ getCustomerPackageName(ticket.customerPackage) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusClass(ticket.statustxt)"
                >
                  {{ getStatusFromText(ticket.statustxt).replace('-', ' ') }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  v-if="ticket.level"
                  class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                  :class="getLevelClass(ticket.level)"
                >
                  {{ ticket.level }}
                </span>
                <span v-else class="text-sm text-gray-500">N/A</span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ ticket.department || 'N/A' }}</div>
                <div v-if="ticket.assignee && ticket.assignee.length > 0" class="text-sm text-gray-500">
                  Assigned: {{ ticket.assignee.join(', ') }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                {{ formatDate(ticket.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="viewTicket(ticket)"
                    class="text-blue-600 hover:text-blue-900 transition-colors p-1 rounded hover:bg-blue-50"
                    title="View Ticket"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editTicket(ticket)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                    title="Edit Ticket"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteTicket(ticket)"
                    class="text-red-600 hover:text-red-900 transition-colors p-1 rounded hover:bg-red-50"
                    title="Delete Ticket"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Ticket Modal -->
    <AddTicketModal v-if="showAddModal" @close="showAddModal = false" @created="handleTicketCreated" />

    <!-- Edit Ticket Modal -->
    <EditTicketModal
      v-if="showEditModal && selectedTicket"
      :ticket="selectedTicket"
      @close="showEditModal = false"
    />

    <!-- View Ticket Modal -->
    <ViewTicketModal
      v-if="showViewModal && selectedTicket"
      :ticket="selectedTicket"
      @close="showViewModal = false"
      @edit="editTicket"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { TicketService, CustomerService, CustomerPackageService } from '@/services/api'
import type { Ticket, Customer, CustomerPackage } from '@/types'
import AddTicketModal from '@/components/modals/AddTicketModal.vue'
import EditTicketModal from '@/components/modals/EditTicketModal.vue'
import ViewTicketModal from '@/components/modals/ViewTicketModal.vue'

// Reactive data
const tickets = ref<Ticket[]>([])
const customers = ref<Customer[]>([])
const customerPackages = ref<CustomerPackage[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const statusFilter = ref('')
const levelFilter = ref('')
const departmentFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedTicket = ref<Ticket | null>(null)

// Computed properties for stats
const ticketStats = computed(() => {
  const statusCounts = tickets.value.reduce((acc, ticket) => {
    const status = getStatusFromText(ticket.statustxt)
    acc[status] = (acc[status] || 0) + 1
    return acc
  }, {} as Record<string, number>)

  return {
    open: statusCounts.open || 0,
    inProgress: statusCounts['in-progress'] || 0,
    resolved: statusCounts.resolved || 0,
    closed: statusCounts.closed || 0
  }
})

const urgentTickets = computed(() =>
  tickets.value.filter(t => t.level?.toLowerCase() === 'urgent')
)

const filteredTickets = computed(() => {
  let filtered = tickets.value

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(ticket =>
      ticket.title.toLowerCase().includes(query) ||
      ticket.description?.toLowerCase().includes(query) ||
      ticket.ticketno.toLowerCase().includes(query) ||
      getCustomerName(ticket.customer).toLowerCase().includes(query)
    )
  }

  // Apply status filter
  if (statusFilter.value) {
    filtered = filtered.filter(ticket => getStatusFromText(ticket.statustxt) === statusFilter.value)
  }

  // Apply level filter
  if (levelFilter.value) {
    filtered = filtered.filter(ticket => ticket.level?.toLowerCase() === levelFilter.value.toLowerCase())
  }

  // Apply department filter
  if (departmentFilter.value) {
    filtered = filtered.filter(ticket => ticket.department?.toLowerCase() === departmentFilter.value.toLowerCase())
  }

  return filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
})

// Helper functions
const getCustomerName = (customerId: string): string => {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.name || 'Unknown Customer'
}

const getCustomerPackageName = (packageId?: string): string => {
  if (!packageId) return 'N/A'
  const pkg = customerPackages.value.find(p => p.id === packageId)
  return pkg?.servicesNo || pkg?.circuitId || 'Unknown Package'
}

const getStatusFromText = (statusTxt?: string): string => {
  if (!statusTxt) return 'open'
  const status = statusTxt.toLowerCase()
  if (status.includes('progress') || status.includes('working')) return 'in-progress'
  if (status.includes('resolved') || status.includes('fixed')) return 'resolved'
  if (status.includes('closed') || status.includes('completed')) return 'closed'
  return 'open'
}

const getStatusClass = (statusTxt?: string): string => {
  const status = getStatusFromText(statusTxt)
  switch (status) {
    case 'open': return 'bg-yellow-100 text-yellow-800'
    case 'in-progress': return 'bg-blue-100 text-blue-800'
    case 'resolved': return 'bg-green-100 text-green-800'
    case 'closed': return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getLevelClass = (level?: string): string => {
  if (!level) return 'bg-gray-100 text-gray-800'
  const levelLower = level.toLowerCase()
  switch (levelLower) {
    case 'urgent': return 'bg-red-100 text-red-800'
    case 'high': return 'bg-orange-100 text-orange-800'
    case 'medium': return 'bg-yellow-100 text-yellow-800'
    case 'low': return 'bg-green-100 text-green-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const formatDate = (dateString: string) => {
  try {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  } catch {
    return dateString
  }
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
  levelFilter.value = ''
  departmentFilter.value = ''
}

const viewTicket = (ticket: Ticket) => {
  selectedTicket.value = ticket
  showViewModal.value = true
}

const editTicket = (ticket: Ticket) => {
  selectedTicket.value = ticket
  showEditModal.value = true
  showViewModal.value = false
}

const deleteTicket = async (ticket: Ticket) => {
  if (confirm(`Are you sure you want to delete ticket "${ticket.title}"?`)) {
    try {
      const response = await TicketService.deleteTicket(ticket.id)
      if (response.success) {
        await loadData()
      } else {
        error.value = response.error || 'Failed to delete ticket'
      }
    } catch (err) {
      error.value = 'Failed to delete ticket'
      console.error('Delete ticket error:', err)
    }
  }
}

const handleTicketCreated = () => {
  showAddModal.value = false
  loadData()
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load all data in parallel
    const [ticketsResponse, customersResponse, packagesResponse] = await Promise.all([
      TicketService.getTickets(),
      CustomerService.getCustomers(),
      CustomerPackageService.getCustomerPackages()
    ])

    if (ticketsResponse.success && ticketsResponse.data?.success && ticketsResponse.data.data) {
      tickets.value = ticketsResponse.data.data
    }

    if (customersResponse.success && customersResponse.data?.success && customersResponse.data.data) {
      customers.value = customersResponse.data.data
    }

    if (packagesResponse.success && packagesResponse.data?.success && packagesResponse.data.data) {
      customerPackages.value = packagesResponse.data.data
    }
  } catch (err) {
    error.value = 'Failed to load data'
    console.error('Load data error:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
