<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Equipment Inventory</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Inventory Item
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search inventory..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div class="flex gap-2">
          <select
            v-model="statusFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            <option value="0">Available</option>
            <option value="1">In Use</option>
            <option value="2">Maintenance</option>
            <option value="3">Retired</option>
          </select>
          <select
            v-model="customerFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Customers</option>
            <option v-for="customer in customers" :key="customer.id" :value="customer.id">
              {{ customer.name }}
            </option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading inventory</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Inventory Table -->
    <div v-else class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Equipment</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Serial Number</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Warranty</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock In</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="item in filteredInventory" :key="item.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ getEquipmentName(item.equipmentId) }}</div>
                <div class="text-sm text-gray-500">{{ item.remark || 'No description' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ item.sn || 'N/A' }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ getCustomerName(item.customerId) }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(item.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getStatusText(item.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">
                  <div v-if="item.warrantyExpDate">
                    <span :class="getWarrantyClass(item.warrantyExpDate)">
                      {{ formatDate(item.warrantyExpDate) }}
                    </span>
                  </div>
                  <div v-else class="text-gray-500">N/A</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(item.stockinDate) || 'N/A' }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="viewInventoryItem(item)"
                    class="text-blue-600 hover:text-blue-900 transition-colors"
                    title="View"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editInventoryItem(item)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors"
                    title="Edit"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteInventoryItem(item)"
                    class="text-red-600 hover:text-red-900 transition-colors"
                    title="Delete"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Placeholder for modals -->
    <div v-if="showAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
        <h3 class="text-lg font-medium mb-4">Add Inventory Item Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showAddModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>

    <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
        <h3 class="text-lg font-medium mb-4">Edit Inventory Item Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showEditModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>

    <div v-if="showViewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4">
        <h3 class="text-lg font-medium mb-4">View Inventory Item Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showViewModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { EquipmentInventoryService, CustomerService } from '@/services/api'
import type { EquipmentInventory, Customer, Equipment } from '@/types'

// Reactive data
const inventory = ref<EquipmentInventory[]>([])
const customers = ref<Customer[]>([])
const equipment = ref<Equipment[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const statusFilter = ref('')
const customerFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedInventoryItem = ref<EquipmentInventory | null>(null)

// Computed properties
const filteredInventory = computed(() => {
  let filtered = inventory.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item => 
      item.sn?.toLowerCase().includes(query) ||
      item.remark?.toLowerCase().includes(query) ||
      getEquipmentName(item.equipmentId).toLowerCase().includes(query) ||
      getCustomerName(item.customerId).toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status.toString() === statusFilter.value)
  }

  if (customerFilter.value) {
    filtered = filtered.filter(item => item.customerId === customerFilter.value)
  }

  return filtered
})

// Helper functions
const getCustomerName = (customerId: string): string => {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.name || 'Unknown Customer'
}

const getEquipmentName = (equipmentId: string): string => {
  const equip = equipment.value.find(e => e.id === equipmentId)
  return equip?.name || 'Unknown Equipment'
}

const getStatusText = (status: number): string => {
  switch (status) {
    case 0: return 'Available'
    case 1: return 'In Use'
    case 2: return 'Maintenance'
    case 3: return 'Retired'
    default: return 'Unknown'
  }
}

const getStatusClass = (status: number): string => {
  switch (status) {
    case 0: return 'bg-green-100 text-green-800'
    case 1: return 'bg-blue-100 text-blue-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    case 3: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getWarrantyClass = (warrantyExpDate: string): string => {
  const expDate = new Date(warrantyExpDate)
  const now = new Date()
  const daysUntilExpiry = Math.ceil((expDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  
  if (daysUntilExpiry < 0) return 'text-red-600 font-medium' // Expired
  if (daysUntilExpiry <= 30) return 'text-orange-600 font-medium' // Expiring soon
  return 'text-green-600' // Valid
}

const formatDate = (dateString?: string): string => {
  if (!dateString) return ''
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}

// Actions
const viewInventoryItem = (item: EquipmentInventory) => {
  selectedInventoryItem.value = item
  showViewModal.value = true
}

const editInventoryItem = (item: EquipmentInventory) => {
  selectedInventoryItem.value = item
  showEditModal.value = true
}

const deleteInventoryItem = async (item: EquipmentInventory) => {
  if (confirm(`Are you sure you want to delete this inventory item?`)) {
    try {
      const response = await EquipmentInventoryService.deleteEquipmentInventory(item.id)
      if (response.success) {
        await loadData()
      } else {
        error.value = response.error || 'Failed to delete inventory item'
      }
    } catch (err) {
      error.value = 'Failed to delete inventory item'
      console.error('Delete inventory error:', err)
    }
  }
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load customers and inventory in parallel
    const [customersResponse, inventoryResponse] = await Promise.all([
      CustomerService.getCustomers(),
      EquipmentInventoryService.getEquipmentInventory()
    ])

    if (customersResponse.success && customersResponse.data?.success && customersResponse.data.data) {
      customers.value = customersResponse.data.data
    }

    if (inventoryResponse.success && inventoryResponse.data?.success && inventoryResponse.data.data) {
      inventory.value = inventoryResponse.data.data
    }

    // Initialize sample equipment data since we don't have the API endpoint yet
    equipment.value = [
      {
        id: '1',
        name: 'Solar Panel System A',
        model: 'SP-2000',
        serialNumber: 'SP001',
        customerId: customers.value[0]?.id || '1',
        purchaseDate: new Date('2023-01-15'),
        expiryDate: new Date('2025-01-15'),
        status: 'active',
        description: 'Main solar panel system',
        createdAt: new Date('2023-01-15'),
        updatedAt: new Date('2023-01-15')
      },
      {
        id: '2',
        name: 'Inverter Unit B',
        model: 'INV-500',
        serialNumber: 'INV002',
        customerId: customers.value[1]?.id || '2',
        purchaseDate: new Date('2023-03-20'),
        expiryDate: new Date('2025-03-20'),
        status: 'maintenance',
        description: 'Power inverter unit',
        createdAt: new Date('2023-03-20'),
        updatedAt: new Date('2023-03-20')
      }
    ]
  } catch (err) {
    error.value = 'Failed to load data'
    console.error('Load data error:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
