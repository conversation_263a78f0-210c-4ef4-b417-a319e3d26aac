<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Equipment</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Equipment
      </button>
    </div>

    <!-- Stats Cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-5">
      <div class="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
        <div class="flex items-center">
          <div class="p-2 md:p-3 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 md:w-7 md:h-7 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4 md:ml-5">
            <p class="text-sm md:text-base font-medium text-gray-600">Active Equipment</p>
            <p class="text-lg font-semibold text-gray-900">{{ totalEquipment }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 rounded-lg">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Expiring Soon</p>
            <p class="text-lg font-semibold text-gray-900">{{ expiringEquipment.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white p-6 rounded-lg shadow">
        <div class="flex items-center">
          <div class="p-2 bg-red-100 rounded-lg">
            <svg class="w-6 h-6 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">Expired</p>
            <p class="text-lg font-semibold text-gray-900">{{ expiredEquipment.length }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex items-center space-x-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search equipment..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <select
          v-model="statusFilter"
          class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        >
          <option value="">All Status</option>
          <option value="1">Active</option>
          <option value="2">Maintenance</option>
          <option value="3">Expired</option>
          <option value="0">Inactive</option>
        </select>
        <button
          @click="clearFilters"
          class="px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors"
        >
          Clear
        </button>
      </div>
    </div>

    <!-- Equipment Table -->
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-base font-medium text-gray-900">
          All Equipment ({{ filteredEquipment.length }})
        </h3>
      </div>

      <div v-if="filteredEquipment.length === 0" class="p-6 text-center text-gray-500">
        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
        <p class="mt-2">No equipment found</p>
        <p class="text-sm text-gray-400">Get started by adding your first equipment</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Equipment
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Vendor & Category
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Warranty Expiry
              </th>
              <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="equipment in filteredEquipment"
              :key="equipment.id"
              class="hover:bg-gray-50 transition-colors"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ equipment.name }}</div>
                  <div class="text-sm text-gray-500">{{ equipment.category0 }} • {{ equipment.category1 }} • {{ equipment.sn || 'No S/N' }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm text-gray-900">{{ getVendorName(equipment.vendorId) }}</div>
                <div class="text-sm text-gray-500">{{ equipment.category2 }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="inline-flex px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusClass(equipment.status)"
                >
                  {{ getStatusText(equipment.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="equipment.warrantyExpDate" class="text-sm text-gray-900">{{ formatDate(new Date(equipment.warrantyExpDate)) }}</div>
                <div v-else class="text-sm text-gray-500">No warranty date</div>
                <div
                  v-if="equipment.warrantyExpDate"
                  class="text-xs"
                  :class="{
                    'text-red-600': isExpired(equipment.warrantyExpDate),
                    'text-yellow-600': isExpiringSoon(equipment.warrantyExpDate),
                    'text-gray-500': !isExpired(equipment.warrantyExpDate) && !isExpiringSoon(equipment.warrantyExpDate)
                  }"
                >
                  {{ getExpiryStatus(equipment.warrantyExpDate) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <button
                    @click="viewEquipment(equipment)"
                    class="text-blue-600 hover:text-blue-900 transition-colors p-1 rounded hover:bg-blue-50"
                    title="View Equipment"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editEquipment(equipment)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors p-1 rounded hover:bg-indigo-50"
                    title="Edit Equipment"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteEquipment(equipment)"
                    class="text-red-600 hover:text-red-900 transition-colors p-1 rounded hover:bg-red-50"
                    title="Delete Equipment"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Equipment Modal -->
    <AddEquipmentModal v-if="showAddModal" @close="showAddModal = false" />

    <!-- Edit Equipment Modal -->
    <EditEquipmentModal
      v-if="showEditModal && selectedEquipment"
      :equipment="selectedEquipment"
      @close="showEditModal = false"
    />

    <!-- View Equipment Modal -->
    <ViewEquipmentModal
      v-if="showViewModal && selectedEquipment"
      :equipment="selectedEquipment"
      @close="showViewModal = false"
      @edit="editEquipment"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CustomerService } from '@/services/api'
import type { Equipment, Customer } from '@/types'
import AddEquipmentModal from '@/components/modals/AddEquipmentModal.vue'
import EditEquipmentModal from '@/components/modals/EditEquipmentModal.vue'
import ViewEquipmentModal from '@/components/modals/ViewEquipmentModal.vue'

// Reactive data
const equipment = ref<Equipment[]>([])
const customers = ref<Customer[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const statusFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedEquipment = ref<Equipment | null>(null)

const filteredEquipment = computed(() => {
  let filtered = equipment.value

  // Apply search filter
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(query) ||
      (item.sn && item.sn.toLowerCase().includes(query)) ||
      item.category0.toLowerCase().includes(query) ||
      item.category1.toLowerCase().includes(query) ||
      item.category2.toLowerCase().includes(query)
    )
  }

  // Apply status filter
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === parseInt(statusFilter.value))
  }

  return filtered
})

const getVendorName = (vendorId: string) => {
  // For now, return a placeholder since we don't have vendor data
  return `Vendor ${vendorId}`
}

const getStatusText = (status: number) => {
  switch (status) {
    case 1: return 'Active'
    case 2: return 'Maintenance'
    case 3: return 'Expired'
    case 0: return 'Inactive'
    default: return 'Unknown'
  }
}

const getStatusClass = (status: number) => {
  switch (status) {
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    case 3: return 'bg-red-100 text-red-800'
    case 0: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// Computed properties for stats
const totalEquipment = computed(() => equipment.value.length)

const expiringEquipment = computed(() => {
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  return equipment.value.filter(item => {
    if (!item.warrantyExpDate) return false
    const expiryDate = new Date(item.warrantyExpDate)
    return expiryDate >= now && expiryDate <= thirtyDaysFromNow
  })
})

const expiredEquipment = computed(() => {
  const now = new Date()
  return equipment.value.filter(item => {
    if (!item.warrantyExpDate) return false
    return new Date(item.warrantyExpDate) < now
  })
})

const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric'
  })
}

const isExpired = (date: string | Date) => {
  return new Date(date) < new Date()
}

const isExpiringSoon = (date: string | Date) => {
  const now = new Date()
  const thirtyDaysFromNow = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000)
  const expiryDate = new Date(date)
  return expiryDate >= now && expiryDate <= thirtyDaysFromNow
}

const getExpiryStatus = (date: string | Date) => {
  if (isExpired(date)) {
    return 'Expired'
  } else if (isExpiringSoon(date)) {
    return 'Expires soon'
  }
  return 'Active'
}

const clearFilters = () => {
  searchQuery.value = ''
  statusFilter.value = ''
}

const viewEquipment = (equipment: Equipment) => {
  selectedEquipment.value = equipment
  showViewModal.value = true
}

const editEquipment = (equipment: Equipment) => {
  selectedEquipment.value = equipment
  showEditModal.value = true
  showViewModal.value = false
}

const deleteEquipment = async (equipmentItem: Equipment) => {
  if (confirm(`Are you sure you want to delete ${equipmentItem.name}?`)) {
    // Since we don't have an equipment API endpoint yet, just remove from local array
    equipment.value = equipment.value.filter(e => e.id !== equipmentItem.id)
  }
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load customers from API
    const customersResponse = await CustomerService.getCustomers()
    if (customersResponse.success && customersResponse.data && customersResponse.data.success && customersResponse.data.data) {
      customers.value = customersResponse.data.data
    } else if (customersResponse.success && customersResponse.data && customersResponse.data.success && customersResponse.data.data === null) {
      // Handle case where API returns null data (empty list)
      customers.value = []
    }

    // Initialize sample equipment data since we don't have the API endpoint yet
    equipment.value = [
      {
        id: '1',
        vendorId: 'vendor1',
        category0: 'Solar',
        category1: 'Panels',
        category2: 'Residential',
        orderId: 'order1',
        name: 'Solar Panel System A',
        sn: 'SP001',
        warrantyPeriod: 24,
        warrantyStartDate: '2023-01-15',
        warrantyExpDate: '2025-01-15',
        status: 1, // 1 = active
        remark: 'Main solar panel system',
        stockinDate: '2023-01-15',
        stockinBy: 'user1',
        createBy: 'user1',
        isDelete: false,
        createdAt: '2023-01-15T00:00:00Z',
        updatedAt: '2023-01-15T00:00:00Z'
      },
      {
        id: '2',
        vendorId: 'vendor2',
        category0: 'Power',
        category1: 'Inverters',
        category2: 'Commercial',
        orderId: 'order2',
        name: 'Inverter Unit B',
        sn: 'INV002',
        warrantyPeriod: 36,
        warrantyStartDate: '2023-03-20',
        warrantyExpDate: '2026-03-20',
        status: 2, // 2 = maintenance
        remark: 'Power inverter unit',
        stockinDate: '2023-03-20',
        stockinBy: 'user1',
        createBy: 'user1',
        isDelete: false,
        createdAt: '2023-03-20T00:00:00Z',
        updatedAt: '2023-03-20T00:00:00Z'
      }
    ]
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Failed to load data'
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
