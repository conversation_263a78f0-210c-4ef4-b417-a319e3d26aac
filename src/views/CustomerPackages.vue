<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Customer Packages</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Package
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search packages..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div class="flex gap-2">
          <select
            v-model="statusFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Status</option>
            <option value="0">Inactive</option>
            <option value="1">Active</option>
            <option value="2">Suspended</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading packages</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Packages Table -->
    <div v-else class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Circuit ID</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service No</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Speed</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="pkg in filteredPackages" :key="pkg.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="text-sm font-medium text-gray-900">{{ getCustomerName(pkg.customerId) }}</div>
                <div class="text-sm text-gray-500">{{ pkg.companyName || 'N/A' }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.circuitId || 'N/A' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.servicesNo || 'N/A' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.speed || 'N/A' }}</td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{{ pkg.provider || 'N/A' }}</td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getStatusClass(pkg.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getStatusText(pkg.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="viewPackage(pkg)"
                    class="text-blue-600 hover:text-blue-900 transition-colors"
                    title="View"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editPackage(pkg)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors"
                    title="Edit"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="deletePackage(pkg)"
                    class="text-red-600 hover:text-red-900 transition-colors"
                    title="Delete"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Add Package Modal -->
    <AddCustomerPackageModal v-if="showAddModal" @close="showAddModal = false" @success="loadData" />

    <!-- Edit Package Modal -->
    <EditCustomerPackageModal
      v-if="showEditModal && selectedPackage"
      :package="selectedPackage"
      @close="showEditModal = false"
      @success="loadData"
    />

    <!-- View Package Modal -->
    <ViewCustomerPackageModal
      v-if="showViewModal && selectedPackage"
      :package="selectedPackage"
      @close="showViewModal = false"
      @edit="editPackage"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { CustomerPackageService, CustomerService } from '@/services/api'
import type { CustomerPackage, Customer } from '@/types'
import AddCustomerPackageModal from '@/components/modals/AddCustomerPackageModal.vue'
import EditCustomerPackageModal from '@/components/modals/EditCustomerPackageModal.vue'
import ViewCustomerPackageModal from '@/components/modals/ViewCustomerPackageModal.vue'

// Reactive data
const packages = ref<CustomerPackage[]>([])
const customers = ref<Customer[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const statusFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedPackage = ref<CustomerPackage | null>(null)

// Computed properties
const filteredPackages = computed(() => {
  let filtered = packages.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(pkg => 
      pkg.circuitId?.toLowerCase().includes(query) ||
      pkg.servicesNo?.toLowerCase().includes(query) ||
      pkg.provider?.toLowerCase().includes(query) ||
      pkg.companyName?.toLowerCase().includes(query) ||
      getCustomerName(pkg.customerId).toLowerCase().includes(query)
    )
  }

  if (statusFilter.value) {
    filtered = filtered.filter(pkg => pkg.status.toString() === statusFilter.value)
  }

  return filtered
})

// Helper functions
const getCustomerName = (customerId: string): string => {
  const customer = customers.value.find(c => c.id === customerId)
  return customer?.name || 'Unknown Customer'
}

const getStatusText = (status: number): string => {
  switch (status) {
    case 0: return 'Inactive'
    case 1: return 'Active'
    case 2: return 'Suspended'
    default: return 'Unknown'
  }
}

const getStatusClass = (status: number): string => {
  switch (status) {
    case 0: return 'bg-gray-100 text-gray-800'
    case 1: return 'bg-green-100 text-green-800'
    case 2: return 'bg-yellow-100 text-yellow-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

// Actions
const viewPackage = (pkg: CustomerPackage) => {
  selectedPackage.value = pkg
  showViewModal.value = true
}

const editPackage = (pkg: CustomerPackage) => {
  selectedPackage.value = pkg
  showEditModal.value = true
  showViewModal.value = false
}

const deletePackage = async (pkg: CustomerPackage) => {
  if (confirm(`Are you sure you want to delete this package?`)) {
    try {
      const response = await CustomerPackageService.deleteCustomerPackage(pkg.id)
      if (response.success) {
        await loadData()
      } else {
        error.value = response.error || 'Failed to delete package'
      }
    } catch (err) {
      error.value = 'Failed to delete package'
      console.error('Delete package error:', err)
    }
  }
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    // Load customers and packages in parallel
    const [customersResponse, packagesResponse] = await Promise.all([
      CustomerService.getCustomers(),
      CustomerPackageService.getCustomerPackages()
    ])

    if (customersResponse.success && customersResponse.data?.success && customersResponse.data.data) {
      customers.value = customersResponse.data.data
    }

    if (packagesResponse.success && packagesResponse.data?.success && packagesResponse.data.data) {
      packages.value = packagesResponse.data.data
    }
  } catch (err) {
    error.value = 'Failed to load data'
    console.error('Load data error:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
