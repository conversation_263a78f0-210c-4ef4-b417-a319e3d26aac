<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
      <h1 class="text-lg font-bold text-gray-900">Equipment Categories</h1>
      <button
        @click="showAddModal = true"
        class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors font-medium"
      >
        Add Category
      </button>
    </div>

    <!-- Search and Filters -->
    <div class="bg-white p-4 rounded-lg shadow">
      <div class="flex flex-col sm:flex-row gap-4">
        <div class="flex-1">
          <input
            v-model="searchQuery"
            type="text"
            placeholder="Search categories..."
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
        <div class="flex gap-2">
          <select
            v-model="categoryTypeFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Types</option>
            <option value="1">Hardware</option>
            <option value="2">Software</option>
            <option value="3">Service</option>
            <option value="4">Other</option>
          </select>
          <select
            v-model="parentFilter"
            class="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">All Levels</option>
            <option value="root">Root Categories</option>
            <option value="child">Sub Categories</option>
          </select>
        </div>
      </div>
    </div>

    <!-- Loading State -->
    <div v-if="loading" class="flex justify-center items-center py-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 rounded-md p-4">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <h3 class="text-sm font-medium text-red-800">Error loading categories</h3>
          <div class="mt-2 text-sm text-red-700">{{ error }}</div>
        </div>
      </div>
    </div>

    <!-- Categories Table -->
    <div v-else class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category Name</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Parent Category</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Level</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sub Categories</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="category in filteredCategories" :key="category.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div :class="category.parentId ? 'ml-6' : ''" class="flex items-center">
                    <svg v-if="category.parentId" class="w-4 h-4 text-gray-400 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                    </svg>
                    <div class="text-sm font-medium text-gray-900">{{ category.name }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="getCategoryTypeClass(category.categoryType)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ getCategoryTypeText(category.categoryType) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ getParentCategoryName(category.parentId) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span :class="category.parentId ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800'" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
                  {{ category.parentId ? 'Sub Category' : 'Root Category' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ getSubCategoryCount(category.id) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                {{ formatDate(category.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <div class="flex space-x-2">
                  <button
                    @click="viewCategory(category)"
                    class="text-blue-600 hover:text-blue-900 transition-colors"
                    title="View"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  </button>
                  <button
                    @click="editCategory(category)"
                    class="text-indigo-600 hover:text-indigo-900 transition-colors"
                    title="Edit"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button
                    @click="addSubCategory(category)"
                    class="text-green-600 hover:text-green-900 transition-colors"
                    title="Add Sub Category"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                  <button
                    @click="deleteCategory(category)"
                    class="text-red-600 hover:text-red-900 transition-colors"
                    title="Delete"
                  >
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <!-- Placeholder for modals -->
    <div v-if="showAddModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
        <h3 class="text-lg font-medium mb-4">Add Category Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showAddModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>

    <div v-if="showEditModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-md w-full mx-4">
        <h3 class="text-lg font-medium mb-4">Edit Category Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showEditModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>

    <div v-if="showViewModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-lg shadow-lg max-w-2xl w-full mx-4">
        <h3 class="text-lg font-medium mb-4">View Category Modal</h3>
        <p class="text-gray-600 mb-4">Modal implementation coming soon...</p>
        <button @click="showViewModal = false" class="px-4 py-2 bg-gray-300 rounded-md">Close</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { EquipmentCategoryService } from '@/services/api'
import type { EquipmentCategory } from '@/types'

// Reactive data
const categories = ref<EquipmentCategory[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

const searchQuery = ref('')
const categoryTypeFilter = ref('')
const parentFilter = ref('')
const showAddModal = ref(false)
const showEditModal = ref(false)
const showViewModal = ref(false)
const selectedCategory = ref<EquipmentCategory | null>(null)

// Computed properties
const filteredCategories = computed(() => {
  let filtered = categories.value

  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(category => 
      category.name.toLowerCase().includes(query) ||
      getParentCategoryName(category.parentId).toLowerCase().includes(query)
    )
  }

  if (categoryTypeFilter.value) {
    filtered = filtered.filter(category => category.categoryType.toString() === categoryTypeFilter.value)
  }

  if (parentFilter.value) {
    if (parentFilter.value === 'root') {
      filtered = filtered.filter(category => !category.parentId)
    } else if (parentFilter.value === 'child') {
      filtered = filtered.filter(category => category.parentId)
    }
  }

  // Sort by parent/child hierarchy
  return filtered.sort((a, b) => {
    if (!a.parentId && b.parentId) return -1
    if (a.parentId && !b.parentId) return 1
    return a.name.localeCompare(b.name)
  })
})

// Helper functions
const getParentCategoryName = (parentId?: string): string => {
  if (!parentId) return 'Root Category'
  const parent = categories.value.find(c => c.id === parentId)
  return parent?.name || 'Unknown Parent'
}

const getCategoryTypeText = (categoryType: number): string => {
  switch (categoryType) {
    case 1: return 'Hardware'
    case 2: return 'Software'
    case 3: return 'Service'
    case 4: return 'Other'
    default: return 'Unknown'
  }
}

const getCategoryTypeClass = (categoryType: number): string => {
  switch (categoryType) {
    case 1: return 'bg-blue-100 text-blue-800'
    case 2: return 'bg-green-100 text-green-800'
    case 3: return 'bg-yellow-100 text-yellow-800'
    case 4: return 'bg-gray-100 text-gray-800'
    default: return 'bg-gray-100 text-gray-800'
  }
}

const getSubCategoryCount = (categoryId: string): number => {
  return categories.value.filter(c => c.parentId === categoryId).length
}

const formatDate = (dateString: string): string => {
  try {
    return new Date(dateString).toLocaleDateString()
  } catch {
    return dateString
  }
}

// Actions
const viewCategory = (category: EquipmentCategory) => {
  selectedCategory.value = category
  showViewModal.value = true
}

const editCategory = (category: EquipmentCategory) => {
  selectedCategory.value = category
  showEditModal.value = true
}

const addSubCategory = (parentCategory: EquipmentCategory) => {
  // Set parent category for new sub category
  selectedCategory.value = parentCategory
  showAddModal.value = true
}

const deleteCategory = async (category: EquipmentCategory) => {
  const subCategoryCount = getSubCategoryCount(category.id)
  if (subCategoryCount > 0) {
    alert(`Cannot delete category "${category.name}" because it has ${subCategoryCount} sub-categories. Please delete or move the sub-categories first.`)
    return
  }

  if (confirm(`Are you sure you want to delete "${category.name}"?`)) {
    try {
      const response = await EquipmentCategoryService.deleteEquipmentCategory(category.id)
      if (response.success) {
        await loadData()
      } else {
        error.value = response.error || 'Failed to delete category'
      }
    } catch (err) {
      error.value = 'Failed to delete category'
      console.error('Delete category error:', err)
    }
  }
}

const loadData = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await EquipmentCategoryService.getEquipmentCategories()
    if (response.success && response.data?.success && response.data.data) {
      categories.value = response.data.data
    }
  } catch (err) {
    error.value = 'Failed to load categories'
    console.error('Load categories error:', err)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadData()
})
</script>
