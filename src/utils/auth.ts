import { ref, computed, readonly } from 'vue'
import { AuthService } from '@/services/api'
import type { UserResponse } from '@/types'

// Global reactive state for authentication
const token = ref<string | null>(localStorage.getItem('auth_token'))
const currentUser = ref<UserResponse | null>(null)
const loading = ref(false)
const error = ref<string | null>(null)

// Initialize auth token if it exists
if (token.value) {
  AuthService.setToken(token.value)
}

// Computed properties
export const isAuthenticated = computed(() => token.value !== null)

// Authentication functions
export const login = async (credentials: { login: string; password: string }) => {
  loading.value = true
  error.value = null
  
  try {
    const response = await AuthService.login(credentials)

    if (response.success && response.data && response.data.success && response.data.data) {
      const authToken = response.data.data.token
      const user = response.data.data.user

      if (!authToken) {
        error.value = 'No token received from server'
        return null
      }

      // Store token
      token.value = authToken
      localStorage.setItem('auth_token', authToken)
      AuthService.setToken(authToken)

      // Set current user from login response
      currentUser.value = user

      // Also fetch the complete user profile to ensure we have all data
      try {
        const profileResponse = await AuthService.getCurrentUser()
        if (profileResponse.success && profileResponse.data && profileResponse.data.success && profileResponse.data.data) {
          currentUser.value = profileResponse.data.data
        }
      } catch (profileError) {
        console.warn('Failed to fetch user profile after login:', profileError)
        // Continue with the user data from login response
      }

      return { token: authToken, user }
    } else {
      error.value = response.error || 'Login failed'
      return null
    }
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error occurred'
    return null
  } finally {
    loading.value = false
  }
}

export const logout = async () => {
  loading.value = true
  error.value = null
  
  try {
    // Call logout endpoint (but don't fail if it doesn't work since backend is buggy)
    try {
      await AuthService.logout()
    } catch (logoutError) {
      console.warn('Logout API call failed, but continuing with local cleanup:', logoutError)
    }
    
    // Clear local state regardless of API call result
    token.value = null
    currentUser.value = null
    localStorage.removeItem('auth_token')
    AuthService.removeToken()
    
    return true
  } catch (err) {
    error.value = err instanceof Error ? err.message : 'Unknown error occurred'
    
    // Even if there's an error, clear local state
    token.value = null
    currentUser.value = null
    localStorage.removeItem('auth_token')
    AuthService.removeToken()
    
    return false
  } finally {
    loading.value = false
  }
}

export const checkAuthStatus = async () => {
  if (!token.value) {
    return false
  }

  try {
    // Now that the profile API is working, let's use it to validate the token
    const response = await AuthService.getCurrentUser()
    if (response.success && response.data && response.data.success && response.data.data) {
      currentUser.value = response.data.data
      return true
    } else {
      // Token is invalid, clear it
      clearToken()
      return false
    }
  } catch (err) {
    console.error('Auth status check failed:', err)
    // Token is invalid, clear it
    clearToken()
    return false
  }
}

export const setToken = (newToken: string) => {
  token.value = newToken
  localStorage.setItem('auth_token', newToken)
  AuthService.setToken(newToken)
}

export const clearToken = () => {
  token.value = null
  currentUser.value = null
  localStorage.removeItem('auth_token')
  AuthService.removeToken()
}

// Export reactive refs for components to use
export const authState = {
  token: readonly(token),
  currentUser: readonly(currentUser),
  loading: readonly(loading),
  error: readonly(error),
  isAuthenticated
}

// Export mutable refs for internal use
export const authMutable = {
  token,
  currentUser,
  loading,
  error
}

// Initialize auth status if token exists
export const initializeAuth = async () => {
  if (token.value) {
    try {
      await checkAuthStatus()
    } catch (error) {
      console.warn('Invalid token found on initialization, clearing...', error)
      clearToken()
    }
  }
}

// Auto-initialize when module is imported
initializeAuth().catch(console.error)
