import axios, { type AxiosInstance, type AxiosError } from 'axios'
import type {
  UserResponse,
  Customer,
  CreateCustomerRequest,
  UpdateCustomerRequest,
  CustomerPackage,
  CreateCustomerPackageRequest,
  UpdateCustomerPackageRequest,
  CustomerDocument,
  CreateCustomerDocumentRequest,
  UpdateCustomerDocumentRequest,
  CustomerServices,
  CreateCustomerServicesRequest,
  UpdateCustomerServicesRequest,
  Equipment,
  CreateEquipmentRequest,
  UpdateEquipmentRequest,
  EquipmentInventory,
  CreateEquipmentInventoryRequest,
  UpdateEquipmentInventoryRequest,
  EquipmentCategory,
  CreateEquipmentCategoryRequest,
  UpdateEquipmentCategoryRequest,
  Ticket,
  CreateTicketRequest,
  UpdateTicketRequest,
  ApiResponse,
  InnerApiResponse
} from '@/types'

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://cmsapi.jenexusenergy.com/api/v1'

// API Client class
class ApiClient {
  private axiosInstance: AxiosInstance

  constructor(baseURL: string) {
    this.axiosInstance = axios.create({
      baseURL,
      headers: {
        'Content-Type': 'application/json',
      },
      timeout: 10000, // 10 second timeout
    })

    // Add response interceptor for consistent error handling
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error: AxiosError) => {
        console.error('API Error:', error)
        return Promise.reject(error)
      }
    )
  }

  // Set authorization token
  setAuthToken(token: string) {
    this.axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${token}`
  }

  // Remove authorization token
  removeAuthToken() {
    delete this.axiosInstance.defaults.headers.common['Authorization']
  }

  // Generic request method
  private async request<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    data?: any
  ): Promise<ApiResponse<T>> {
    try {
      const response = await this.axiosInstance.request({
        url: endpoint,
        method,
        data,
      })

      const rawData = response.data

      // Handle the server response structure
      // The server returns: { success: true, data: actualData } or { success: false, error: errorMessage }
      if (rawData.success && rawData.data !== undefined) {
        // Wrap the response to match our expected structure
        return {
          success: true,
          data: {
            success: true,
            data: rawData.data,
            timestamp: new Date().toISOString()
          } as InnerApiResponse<T>
        }
      } else {
        return {
          success: false,
          error: rawData.error || rawData.message || 'API request failed',
        }
      }
    } catch (error) {
      // Handle Axios errors
      if (axios.isAxiosError(error)) {
        const axiosError = error as AxiosError
        if (axiosError.response) {
          // Server responded with error status
          const errorData = axiosError.response.data as any
          return {
            success: false,
            error: errorData?.message || errorData?.error || `HTTP error! status: ${axiosError.response.status}`,
          }
        } else if (axiosError.request) {
          // Request was made but no response received
          return {
            success: false,
            error: 'Network error: No response from server',
          }
        } else {
          // Something else happened
          return {
            success: false,
            error: axiosError.message || 'Request setup error',
          }
        }
      }

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      }
    }
  }

  // GET request
  async get<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'GET')
  }

  // POST request
  async post<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'POST', data)
  }

  // PUT request
  async put<T>(endpoint: string, data?: any): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'PUT', data)
  }

  // DELETE request
  async delete<T>(endpoint: string): Promise<ApiResponse<T>> {
    return this.request<T>(endpoint, 'DELETE')
  }


}

// Create API client instance
export const apiClient = new ApiClient(API_BASE_URL)

// User API Service
export class UserService {
  // Get user by ID
  static async getUserById(id: string): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>(`/users/${id}`)
  }

  // Get all users
  static async getUsers(): Promise<ApiResponse<UserResponse[]>> {
    return apiClient.get<UserResponse[]>('/users')
  }

  // Update user profile
  static async updateUser(id: string, data: Partial<UserResponse>): Promise<ApiResponse<UserResponse>> {
    return apiClient.put<UserResponse>(`/users/${id}`, data)
  }
}

// Customer API Service
export class CustomerService {
  // Get all customers
  static async getCustomers(): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>('/customers')
  }

  // Get customer by ID
  static async getCustomerById(id: string): Promise<ApiResponse<Customer>> {
    return apiClient.get<Customer>(`/customers/${id}`)
  }

  // Create new customer
  static async createCustomer(data: CreateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.post<Customer>('/customers', data)
  }

  // Update customer
  static async updateCustomer(id: string, data: UpdateCustomerRequest): Promise<ApiResponse<Customer>> {
    return apiClient.put<Customer>(`/customers/${id}`, data)
  }

  // Delete customer (soft delete)
  static async deleteCustomer(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customers/${id}`)
  }

  // Search customers
  static async searchCustomers(query: string): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>(`/customers/search?q=${encodeURIComponent(query)}`)
  }

  // Get customers by parent ID
  static async getCustomersByParent(parentId: string): Promise<ApiResponse<Customer[]>> {
    return apiClient.get<Customer[]>(`/customers?parentId=${parentId}`)
  }
}

// Customer Package API Service
export class CustomerPackageService {
  // Get all customer packages
  static async getCustomerPackages(): Promise<ApiResponse<CustomerPackage[]>> {
    return apiClient.get<CustomerPackage[]>('/customer-packages')
  }

  // Get customer package by ID
  static async getCustomerPackageById(id: string): Promise<ApiResponse<CustomerPackage>> {
    return apiClient.get<CustomerPackage>(`/customer-packages/${id}`)
  }

  // Get customer packages by customer ID
  static async getCustomerPackagesByCustomerId(customerId: string): Promise<ApiResponse<CustomerPackage[]>> {
    return apiClient.get<CustomerPackage[]>(`/customer-packages/customer/${customerId}`)
  }

  // Create new customer package
  static async createCustomerPackage(data: CreateCustomerPackageRequest): Promise<ApiResponse<CustomerPackage>> {
    return apiClient.post<CustomerPackage>('/customer-packages', data)
  }

  // Update customer package
  static async updateCustomerPackage(id: string, data: UpdateCustomerPackageRequest): Promise<ApiResponse<CustomerPackage>> {
    return apiClient.put<CustomerPackage>(`/customer-packages/${id}`, data)
  }

  // Delete customer package (soft delete)
  static async deleteCustomerPackage(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customer-packages/${id}`)
  }

  // Search customer packages
  static async searchCustomerPackages(query: string): Promise<ApiResponse<CustomerPackage[]>> {
    return apiClient.get<CustomerPackage[]>(`/customer-packages/search?q=${encodeURIComponent(query)}`)
  }
}

// Customer Document API Service
export class CustomerDocumentService {
  // Get all customer documents
  static async getCustomerDocuments(): Promise<ApiResponse<CustomerDocument[]>> {
    return apiClient.get<CustomerDocument[]>('/customer-documents')
  }

  // Get customer document by ID
  static async getCustomerDocumentById(id: string): Promise<ApiResponse<CustomerDocument>> {
    return apiClient.get<CustomerDocument>(`/customer-documents/${id}`)
  }

  // Get customer documents by customer ID
  static async getCustomerDocumentsByCustomerId(customerId: string): Promise<ApiResponse<CustomerDocument[]>> {
    return apiClient.get<CustomerDocument[]>(`/customer-documents/customer/${customerId}`)
  }

  // Create new customer document
  static async createCustomerDocument(data: CreateCustomerDocumentRequest): Promise<ApiResponse<CustomerDocument>> {
    return apiClient.post<CustomerDocument>('/customer-documents', data)
  }

  // Update customer document
  static async updateCustomerDocument(id: string, data: UpdateCustomerDocumentRequest): Promise<ApiResponse<CustomerDocument>> {
    return apiClient.put<CustomerDocument>(`/customer-documents/${id}`, data)
  }

  // Delete customer document (soft delete)
  static async deleteCustomerDocument(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customer-documents/${id}`)
  }
}

// Customer Services API Service
export class CustomerServicesService {
  // Get all customer services
  static async getCustomerServices(): Promise<ApiResponse<CustomerServices[]>> {
    return apiClient.get<CustomerServices[]>('/customer-services')
  }

  // Get customer service by ID
  static async getCustomerServiceById(id: string): Promise<ApiResponse<CustomerServices>> {
    return apiClient.get<CustomerServices>(`/customer-services/${id}`)
  }

  // Get customer services by customer ID
  static async getCustomerServicesByCustomerId(customerId: string): Promise<ApiResponse<CustomerServices[]>> {
    return apiClient.get<CustomerServices[]>(`/customer-services/customer/${customerId}`)
  }

  // Create new customer service
  static async createCustomerService(data: CreateCustomerServicesRequest): Promise<ApiResponse<CustomerServices>> {
    return apiClient.post<CustomerServices>('/customer-services', data)
  }

  // Update customer service
  static async updateCustomerService(id: string, data: UpdateCustomerServicesRequest): Promise<ApiResponse<CustomerServices>> {
    return apiClient.put<CustomerServices>(`/customer-services/${id}`, data)
  }

  // Delete customer service (soft delete)
  static async deleteCustomerService(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/customer-services/${id}`)
  }

  // Search customer services
  static async searchCustomerServices(query: string): Promise<ApiResponse<CustomerServices[]>> {
    return apiClient.get<CustomerServices[]>(`/customer-services/search?q=${encodeURIComponent(query)}`)
  }
}

// Equipment API Service
export class EquipmentService {
  // Get all equipment
  static async getEquipment(): Promise<ApiResponse<Equipment[]>> {
    return apiClient.get<Equipment[]>('/equipment')
  }

  // Get equipment by ID
  static async getEquipmentById(id: string): Promise<ApiResponse<Equipment>> {
    return apiClient.get<Equipment>(`/equipment/${id}`)
  }

  // Get equipment by vendor ID
  static async getEquipmentByVendorId(vendorId: string): Promise<ApiResponse<Equipment[]>> {
    return apiClient.get<Equipment[]>(`/equipment/vendor/${vendorId}`)
  }

  // Get equipment by category
  static async getEquipmentByCategory(category0: string, category1?: string, category2?: string): Promise<ApiResponse<Equipment[]>> {
    let url = `/equipment/category/${category0}`
    if (category1) url += `/${category1}`
    if (category2) url += `/${category2}`
    return apiClient.get<Equipment[]>(url)
  }

  // Get equipment by order ID
  static async getEquipmentByOrderId(orderId: string): Promise<ApiResponse<Equipment[]>> {
    return apiClient.get<Equipment[]>(`/equipment/order/${orderId}`)
  }

  // Create new equipment
  static async createEquipment(data: CreateEquipmentRequest): Promise<ApiResponse<Equipment>> {
    return apiClient.post<Equipment>('/equipment', data)
  }

  // Update equipment
  static async updateEquipment(id: string, data: UpdateEquipmentRequest): Promise<ApiResponse<Equipment>> {
    return apiClient.put<Equipment>(`/equipment/${id}`, data)
  }

  // Delete equipment (soft delete)
  static async deleteEquipment(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/equipment/${id}`)
  }

  // Search equipment
  static async searchEquipment(query: string): Promise<ApiResponse<Equipment[]>> {
    return apiClient.get<Equipment[]>(`/equipment/search?q=${encodeURIComponent(query)}`)
  }

  // Stock in equipment
  static async stockInEquipment(id: string, stockinBy: string): Promise<ApiResponse<Equipment>> {
    return apiClient.put<Equipment>(`/equipment/${id}/stock-in`, { stockinBy })
  }

  // Stock out equipment
  static async stockOutEquipment(id: string, stockoutBy: string): Promise<ApiResponse<Equipment>> {
    return apiClient.put<Equipment>(`/equipment/${id}/stock-out`, { stockoutBy })
  }
}

// Equipment Inventory API Service
export class EquipmentInventoryService {
  // Get all equipment inventory
  static async getEquipmentInventory(): Promise<ApiResponse<EquipmentInventory[]>> {
    return apiClient.get<EquipmentInventory[]>('/equipment-inventory')
  }

  // Get equipment inventory by ID
  static async getEquipmentInventoryById(id: string): Promise<ApiResponse<EquipmentInventory>> {
    return apiClient.get<EquipmentInventory>(`/equipment-inventory/${id}`)
  }

  // Get equipment inventory by customer ID
  static async getEquipmentInventoryByCustomerId(customerId: string): Promise<ApiResponse<EquipmentInventory[]>> {
    return apiClient.get<EquipmentInventory[]>(`/equipment-inventory/customer/${customerId}`)
  }

  // Get equipment inventory by equipment ID
  static async getEquipmentInventoryByEquipmentId(equipmentId: string): Promise<ApiResponse<EquipmentInventory[]>> {
    return apiClient.get<EquipmentInventory[]>(`/equipment-inventory/equipment/${equipmentId}`)
  }

  // Create new equipment inventory
  static async createEquipmentInventory(data: CreateEquipmentInventoryRequest): Promise<ApiResponse<EquipmentInventory>> {
    return apiClient.post<EquipmentInventory>('/equipment-inventory', data)
  }

  // Update equipment inventory
  static async updateEquipmentInventory(id: string, data: UpdateEquipmentInventoryRequest): Promise<ApiResponse<EquipmentInventory>> {
    return apiClient.put<EquipmentInventory>(`/equipment-inventory/${id}`, data)
  }

  // Delete equipment inventory (soft delete)
  static async deleteEquipmentInventory(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/equipment-inventory/${id}`)
  }

  // Search equipment inventory
  static async searchEquipmentInventory(query: string): Promise<ApiResponse<EquipmentInventory[]>> {
    return apiClient.get<EquipmentInventory[]>(`/equipment-inventory/search?q=${encodeURIComponent(query)}`)
  }
}

// Equipment Category API Service
export class EquipmentCategoryService {
  // Get all equipment categories
  static async getEquipmentCategories(): Promise<ApiResponse<EquipmentCategory[]>> {
    return apiClient.get<EquipmentCategory[]>('/equipment-categories')
  }

  // Get equipment category by ID
  static async getEquipmentCategoryById(id: string): Promise<ApiResponse<EquipmentCategory>> {
    return apiClient.get<EquipmentCategory>(`/equipment-categories/${id}`)
  }

  // Get equipment categories by parent ID
  static async getEquipmentCategoriesByParentId(parentId: string): Promise<ApiResponse<EquipmentCategory[]>> {
    return apiClient.get<EquipmentCategory[]>(`/equipment-categories/parent/${parentId}`)
  }

  // Get root equipment categories (no parent)
  static async getRootEquipmentCategories(): Promise<ApiResponse<EquipmentCategory[]>> {
    return apiClient.get<EquipmentCategory[]>('/equipment-categories/root')
  }

  // Create new equipment category
  static async createEquipmentCategory(data: CreateEquipmentCategoryRequest): Promise<ApiResponse<EquipmentCategory>> {
    return apiClient.post<EquipmentCategory>('/equipment-categories', data)
  }

  // Update equipment category
  static async updateEquipmentCategory(id: string, data: UpdateEquipmentCategoryRequest): Promise<ApiResponse<EquipmentCategory>> {
    return apiClient.put<EquipmentCategory>(`/equipment-categories/${id}`, data)
  }

  // Delete equipment category (soft delete)
  static async deleteEquipmentCategory(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/equipment-categories/${id}`)
  }

  // Search equipment categories
  static async searchEquipmentCategories(query: string): Promise<ApiResponse<EquipmentCategory[]>> {
    return apiClient.get<EquipmentCategory[]>(`/equipment-categories/search?q=${encodeURIComponent(query)}`)
  }
}

// Ticket API Service
export class TicketService {
  // Get all tickets
  static async getTickets(): Promise<ApiResponse<Ticket[]>> {
    return apiClient.get<Ticket[]>('/tickets')
  }

  // Get ticket by ID
  static async getTicketById(id: string): Promise<ApiResponse<Ticket>> {
    return apiClient.get<Ticket>(`/tickets/${id}`)
  }

  // Get tickets by customer ID
  static async getTicketsByCustomerId(customerId: string): Promise<ApiResponse<Ticket[]>> {
    return apiClient.get<Ticket[]>(`/tickets/customer/${customerId}`)
  }

  // Get tickets by customer package ID
  static async getTicketsByCustomerPackageId(customerPackageId: string): Promise<ApiResponse<Ticket[]>> {
    return apiClient.get<Ticket[]>(`/tickets/customer-package/${customerPackageId}`)
  }

  // Get tickets by assignee
  static async getTicketsByAssignee(assignee: string): Promise<ApiResponse<Ticket[]>> {
    return apiClient.get<Ticket[]>(`/tickets/assignee/${assignee}`)
  }

  // Get tickets by department
  static async getTicketsByDepartment(department: string): Promise<ApiResponse<Ticket[]>> {
    return apiClient.get<Ticket[]>(`/tickets/department/${department}`)
  }

  // Create new ticket
  static async createTicket(data: CreateTicketRequest): Promise<ApiResponse<Ticket>> {
    return apiClient.post<Ticket>('/tickets', data)
  }

  // Update ticket
  static async updateTicket(id: string, data: UpdateTicketRequest): Promise<ApiResponse<Ticket>> {
    return apiClient.put<Ticket>(`/tickets/${id}`, data)
  }

  // Delete ticket (soft delete)
  static async deleteTicket(id: string): Promise<ApiResponse<void>> {
    return apiClient.delete<void>(`/tickets/${id}`)
  }

  // Search tickets
  static async searchTickets(query: string): Promise<ApiResponse<Ticket[]>> {
    return apiClient.get<Ticket[]>(`/tickets/search?q=${encodeURIComponent(query)}`)
  }

  // Close ticket
  static async closeTicket(id: string): Promise<ApiResponse<Ticket>> {
    return apiClient.put<Ticket>(`/tickets/${id}/close`)
  }

  // Assign ticket
  static async assignTicket(id: string, assignees: string[]): Promise<ApiResponse<Ticket>> {
    return apiClient.put<Ticket>(`/tickets/${id}/assign`, { assignee: assignees })
  }
}

// Auth Service
export class AuthService {
  // Login
  static async login(credentials: { login: string; password: string }): Promise<ApiResponse<{ token: string; user: UserResponse }>> {
    return apiClient.post<{ token: string; user: UserResponse }>('/auth/login', credentials)
  }

  // Logout
  static async logout(): Promise<ApiResponse<void>> {
    const result = await apiClient.post<void>('/auth/logout')
    apiClient.removeAuthToken()
    return result
  }

  // Get current user profile
  static async getCurrentUser(): Promise<ApiResponse<UserResponse>> {
    return apiClient.get<UserResponse>('/auth/profile')
  }

  // Set token for authenticated requests
  static setToken(token: string) {
    apiClient.setAuthToken(token)
  }

  // Remove token
  static removeToken() {
    apiClient.removeAuthToken()
  }
}

// Export the API client for direct use if needed
export { apiClient as default }
